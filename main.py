"""
客户对话分析流水线主程序
"""
import asyncio
import logging
import json
from pathlib import Path
from config.config import config
from src.utils.logger import setup_logger
from src.langchain_app.pipeline import pipeline


def setup_logging():
    """设置日志"""
    log_dir = Path(config.app.log_file).parent
    log_dir.mkdir(exist_ok=True)

    setup_logger(
        log_file=config.app.log_file,
        log_level=config.app.log_level
    )


def create_sample_data():
    """创建示例数据"""
    sample_data = [
        {
            "conversation_id": "conv_001",
            "customer_id": "cust_001",
            "text": "我想了解一下你们的产品价格和功能特点",
            "timestamp": "2024-12-19 10:00:00"
        },
        {
            "conversation_id": "conv_002",
            "customer_id": "cust_002",
            "text": "产品使用过程中遇到了一些技术问题，需要技术支持",
            "timestamp": "2024-12-19 10:30:00"
        },
        {
            "conversation_id": "conv_003",
            "customer_id": "cust_003",
            "text": "对服务质量不满意，希望能够改进客户服务",
            "timestamp": "2024-12-19 11:00:00"
        },
        {
            "conversation_id": "conv_004",
            "customer_id": "cust_001",
            "text": "想要升级到高级版本，请问有什么优惠政策",
            "timestamp": "2024-12-19 11:30:00"
        },
        {
            "conversation_id": "conv_005",
            "customer_id": "cust_004",
            "text": "产品功能很好用，推荐给朋友了",
            "timestamp": "2024-12-19 12:00:00"
        }
    ]

    # 保存示例数据
    data_dir = Path("data")
    data_dir.mkdir(exist_ok=True)

    sample_file = data_dir / "sample_conversations.json"
    with open(sample_file, 'w', encoding='utf-8') as f:
        json.dump(sample_data, f, ensure_ascii=False, indent=2)

    return str(sample_file)


async def run_demo():
    """运行演示"""
    logger = logging.getLogger(__name__)

    try:
        # 1. 初始化流水线
        logger.info("初始化客户对话分析流水线...")
        if not pipeline.initialize():
            logger.error("流水线初始化失败")
            return

        # 2. 创建示例数据
        logger.info("创建示例数据...")
        sample_file = create_sample_data()
        logger.info(f"示例数据已保存到: {sample_file}")

        # 3. 处理对话数据
        logger.info("处理对话数据...")
        if not pipeline.process_conversation_data(sample_file, "json"):
            logger.error("处理对话数据失败")
            return

        # 4. 运行分析
        logger.info("运行客户群体分析...")
        results = pipeline.run_analysis(clustering_method="kmeans", n_clusters=3)

        if results:
            logger.info("分析完成！")

            # 输出分析结果摘要
            print("\n" + "="*50)
            print("客户对话分析结果摘要")
            print("="*50)

            data_summary = results.get('data_summary', {})
            print(f"总对话数: {data_summary.get('total_conversations', 0)}")
            print(f"总客户数: {data_summary.get('total_customers', 0)}")
            print(f"处理时间: {results.get('processing_time', 0):.2f} 秒")

            cluster_stats = results.get('cluster_statistics', {})
            print(f"聚类数量: {cluster_stats.get('n_clusters', 0)}")
            print(f"平均聚类大小: {cluster_stats.get('avg_cluster_size', 0):.1f}")

            # 显示主要话题
            topic_results = results.get('topic_results', {})
            main_topics = topic_results.get('main_topics', [])
            if main_topics:
                print("\n主要话题:")
                for i, topic in enumerate(main_topics[:3], 1):
                    print(f"{i}. {topic.get('title', '未知话题')}")

            # 显示关键洞察
            insights = results.get('insights', [])
            if insights:
                print("\n关键洞察:")
                for i, insight in enumerate(insights[:3], 1):
                    print(f"{i}. {insight}")

            # 显示分析报告
            report = results.get('analysis_report', '')
            if report:
                print(f"\n分析报告:\n{report}")

            print("\n" + "="*50)

            # 保存完整结果
            results_file = Path("data") / "analysis_results.json"
            with open(results_file, 'w', encoding='utf-8') as f:
                # 移除不能序列化的数据
                serializable_results = {k: v for k, v in results.items()
                                      if k not in ['topic_results']}  # topic_results包含图对象
                json.dump(serializable_results, f, ensure_ascii=False, indent=2)

            logger.info(f"完整分析结果已保存到: {results_file}")

        else:
            logger.error("分析失败")

        # 5. 清理资源
        pipeline.cleanup()

    except Exception as e:
        logger.error(f"演示运行失败: {e}")
        raise


async def main():
    """主函数"""
    # 设置日志
    setup_logging()
    logger = logging.getLogger(__name__)

    logger.info(f"启动 {config.app.name} v{config.app.version}")

    try:
        # 显示配置信息
        print(f"客户对话分析流水线 v{config.app.version}")
        print(f"LLM模型: {config.llm.model}")
        print(f"LLM端点: {config.llm.endpoint}")
        print(f"向量模型: {config.embedding.model}")
        print(f"Milvus地址: {config.milvus.host}:{config.milvus.port}")
        print("-" * 50)

        # 运行演示
        await run_demo()

        logger.info("程序执行完成")

    except Exception as e:
        logger.error(f"程序运行出错: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
