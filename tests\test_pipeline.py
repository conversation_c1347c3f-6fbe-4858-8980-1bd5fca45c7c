"""
流水线测试脚本
"""
import unittest
import numpy as np
from unittest.mock import Mock, patch
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.data_processing.preprocessor import ConversationPreprocessor
from src.data_processing.text_embedder import TextEmbedder
from src.analysis.customer_analyzer import CustomerAnalyzer
from src.analysis.topic_discovery import TopicDiscovery


class TestConversationPreprocessor(unittest.TestCase):
    """测试对话预处理器"""
    
    def setUp(self):
        self.preprocessor = ConversationPreprocessor()
    
    def test_preprocess_single_conversation(self):
        """测试单个对话预处理"""
        conversation = {
            'conversation_id': 'test_001',
            'customer_id': 'cust_001',
            'text': '这是一个测试对话',
            'timestamp': '2024-12-19 10:00:00'
        }
        
        result = self.preprocessor._preprocess_single_conversation(conversation)
        
        self.assertIsNotNone(result)
        self.assertEqual(result['conversation_id'], 'test_001')
        self.assertEqual(result['customer_id'], 'cust_001')
        self.assertIn('text', result)
        self.assertIn('timestamp', result)
    
    def test_clean_text(self):
        """测试文本清理"""
        dirty_text = "  这是一个   测试文本！！！  "
        clean_text = self.preprocessor._clean_text(dirty_text)
        
        self.assertEqual(clean_text, "这是一个 测试文本！")
    
    def test_validate_conversation_data(self):
        """测试对话数据验证"""
        conversations = [
            {
                'conversation_id': 'test_001',
                'customer_id': 'cust_001',
                'text': '这是一个有效的对话',
                'timestamp': '2024-12-19 10:00:00'
            },
            {
                'conversation_id': 'test_002',
                'customer_id': 'cust_002',
                'text': '短',  # 太短的文本
                'timestamp': '2024-12-19 10:00:00'
            }
        ]
        
        valid_conversations, errors = self.preprocessor.validate_conversation_data(conversations)
        
        self.assertEqual(len(valid_conversations), 1)
        self.assertEqual(len(errors), 1)


class TestTextEmbedder(unittest.TestCase):
    """测试文本嵌入器"""
    
    def setUp(self):
        # 使用模拟的嵌入器避免实际加载模型
        self.embedder = TextEmbedder()
        self.embedder.model = Mock()
    
    def test_preprocess_text(self):
        """测试文本预处理"""
        text = "  这是一个测试文本  "
        processed = self.embedder._preprocess_text(text)
        
        self.assertEqual(processed, "这是一个测试文本")
    
    def test_preprocess_texts(self):
        """测试批量文本预处理"""
        texts = ["文本1", "文本2", "", "文本3"]
        processed = self.embedder._preprocess_texts(texts)
        
        self.assertEqual(len(processed), 3)  # 空文本被过滤
    
    @patch('src.data_processing.text_embedder.SentenceTransformer')
    def test_encode_texts_mock(self, mock_transformer):
        """测试文本编码（模拟）"""
        # 模拟编码结果
        mock_model = Mock()
        mock_model.encode.return_value = np.array([[0.1, 0.2, 0.3], [0.4, 0.5, 0.6]])
        mock_transformer.return_value = mock_model
        
        embedder = TextEmbedder()
        texts = ["文本1", "文本2"]
        embeddings = embedder.encode_texts(texts)
        
        self.assertEqual(embeddings.shape, (2, 3))


class TestCustomerAnalyzer(unittest.TestCase):
    """测试客户分析器"""
    
    def setUp(self):
        self.analyzer = CustomerAnalyzer()
    
    def test_preprocess_embeddings(self):
        """测试嵌入向量预处理"""
        embeddings = np.random.rand(10, 5)
        processed = self.analyzer.preprocess_embeddings(embeddings, use_pca=False)
        
        self.assertEqual(processed.shape[0], 10)
        # 标准化后的数据应该接近0均值
        self.assertAlmostEqual(np.mean(processed), 0, places=1)
    
    def test_find_optimal_clusters(self):
        """测试寻找最优聚类数"""
        # 创建明显的聚类数据
        cluster1 = np.random.normal(0, 0.1, (20, 2))
        cluster2 = np.random.normal(5, 0.1, (20, 2))
        embeddings = np.vstack([cluster1, cluster2])
        
        optimal_k = self.analyzer.find_optimal_clusters(embeddings, max_clusters=5)
        
        self.assertGreaterEqual(optimal_k, 2)
        self.assertLessEqual(optimal_k, 5)
    
    def test_perform_clustering(self):
        """测试聚类执行"""
        embeddings = np.random.rand(20, 5)
        labels = self.analyzer.perform_clustering(embeddings, method="kmeans", n_clusters=3)
        
        self.assertEqual(len(labels), 20)
        self.assertLessEqual(len(np.unique(labels)), 3)
    
    def test_extract_cluster_keywords(self):
        """测试关键词提取"""
        texts = ["产品价格很好", "服务质量不错", "价格合理服务好"]
        keywords = self.analyzer._extract_cluster_keywords(texts)
        
        self.assertIsInstance(keywords, list)
        self.assertGreater(len(keywords), 0)


class TestTopicDiscovery(unittest.TestCase):
    """测试话题发现"""
    
    def setUp(self):
        self.topic_discovery = TopicDiscovery()
    
    def test_calculate_topic_similarity(self):
        """测试话题相似度计算"""
        cluster1 = {
            'keywords': ['价格', '产品', '功能'],
            'customer_ids': ['cust_001', 'cust_002']
        }
        cluster2 = {
            'keywords': ['价格', '服务', '质量'],
            'customer_ids': ['cust_002', 'cust_003']
        }
        
        similarity = self.topic_discovery._calculate_topic_similarity(cluster1, cluster2)
        
        self.assertGreaterEqual(similarity, 0)
        self.assertLessEqual(similarity, 1)
    
    def test_calculate_topic_importance(self):
        """测试话题重要性计算"""
        topic = {
            'size': 50,
            'unique_customers': 25,
            'sentiment': '正面'
        }
        
        importance = self.topic_discovery._calculate_topic_importance(topic)
        
        self.assertGreaterEqual(importance, 0)
        self.assertLessEqual(importance, 1)
    
    def test_create_topic_from_cluster(self):
        """测试从聚类创建话题"""
        cluster_info = {
            'topic_description': '产品咨询话题',
            'keywords': ['产品', '价格', '功能'],
            'size': 30,
            'unique_customers': 15,
            'customer_ids': ['cust_001', 'cust_002'],
            'sentiment_analysis': {'main_sentiment': '中性'},
            'sample_conversations': ['测试对话1', '测试对话2']
        }
        
        topic = self.topic_discovery._create_topic_from_cluster(1, cluster_info)
        
        self.assertIn('topic_id', topic)
        self.assertIn('title', topic)
        self.assertIn('keywords', topic)
        self.assertIn('importance', topic)


class TestIntegration(unittest.TestCase):
    """集成测试"""
    
    def test_end_to_end_workflow(self):
        """测试端到端工作流"""
        # 准备测试数据
        conversations = [
            {
                'conversation_id': 'conv_001',
                'customer_id': 'cust_001',
                'text': '我想了解产品价格和功能',
                'timestamp': 1703000000
            },
            {
                'conversation_id': 'conv_002',
                'customer_id': 'cust_002',
                'text': '需要技术支持帮助',
                'timestamp': 1703000060
            }
        ]
        
        # 1. 预处理
        preprocessor = ConversationPreprocessor()
        processed_conversations = preprocessor.preprocess_conversations(conversations)
        
        self.assertEqual(len(processed_conversations), 2)
        
        # 2. 生成嵌入（模拟）
        embeddings = np.random.rand(2, 10)
        
        # 3. 聚类分析
        analyzer = CustomerAnalyzer()
        processed_embeddings = analyzer.preprocess_embeddings(embeddings, use_pca=False)
        cluster_labels = analyzer.perform_clustering(processed_embeddings, method="kmeans", n_clusters=2)
        
        self.assertEqual(len(cluster_labels), 2)
        
        # 4. 聚类分析
        cluster_analysis = analyzer.analyze_clusters(processed_conversations, cluster_labels)
        
        self.assertIsInstance(cluster_analysis, dict)


if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2)
