"""
客户群体分析模块
"""
import logging
import numpy as np
from typing import List, Dict, Any, Optional, Tuple
from sklearn.cluster import KMeans, DBSCAN
from sklearn.metrics import silhouette_score, calinski_harabasz_score
from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler
import umap
import hdbscan
from config.config import config
from src.langchain_app.llm_client import llm_client


class CustomerAnalyzer:
    """客户群体分析器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.scaler = StandardScaler()
        self.pca = None
        self.umap_reducer = None
        self.cluster_model = None
        self.cluster_labels = None
        self.cluster_centers = None
    
    def preprocess_embeddings(self, embeddings: np.ndarray, 
                            use_pca: bool = True, 
                            n_components: int = 50) -> np.ndarray:
        """
        预处理嵌入向量
        
        Args:
            embeddings: 原始嵌入向量
            use_pca: 是否使用PCA降维
            n_components: PCA组件数量
        
        Returns:
            np.ndarray: 预处理后的向量
        """
        try:
            if embeddings.shape[0] == 0:
                self.logger.warning("输入嵌入向量为空")
                return embeddings
            
            # 标准化
            embeddings_scaled = self.scaler.fit_transform(embeddings)
            
            # PCA降维（可选）
            if use_pca and embeddings_scaled.shape[1] > n_components:
                self.pca = PCA(n_components=n_components, random_state=42)
                embeddings_processed = self.pca.fit_transform(embeddings_scaled)
                self.logger.info(f"PCA降维: {embeddings.shape[1]} -> {n_components}")
            else:
                embeddings_processed = embeddings_scaled
            
            return embeddings_processed
            
        except Exception as e:
            self.logger.error(f"预处理嵌入向量失败: {e}")
            return embeddings
    
    def find_optimal_clusters(self, embeddings: np.ndarray, 
                            max_clusters: int = 20) -> int:
        """
        寻找最优聚类数量
        
        Args:
            embeddings: 嵌入向量
            max_clusters: 最大聚类数量
        
        Returns:
            int: 最优聚类数量
        """
        try:
            if embeddings.shape[0] < 2:
                return 1
            
            max_clusters = min(max_clusters, embeddings.shape[0] - 1)
            silhouette_scores = []
            calinski_scores = []
            
            for n_clusters in range(2, max_clusters + 1):
                # KMeans聚类
                kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
                labels = kmeans.fit_predict(embeddings)
                
                # 计算评估指标
                sil_score = silhouette_score(embeddings, labels)
                cal_score = calinski_harabasz_score(embeddings, labels)
                
                silhouette_scores.append(sil_score)
                calinski_scores.append(cal_score)
            
            # 选择轮廓系数最高的聚类数
            optimal_clusters = np.argmax(silhouette_scores) + 2
            
            self.logger.info(f"最优聚类数量: {optimal_clusters}")
            return optimal_clusters
            
        except Exception as e:
            self.logger.error(f"寻找最优聚类数量失败: {e}")
            return 3  # 默认返回3个聚类
    
    def perform_clustering(self, embeddings: np.ndarray, 
                         method: str = "kmeans",
                         n_clusters: Optional[int] = None) -> np.ndarray:
        """
        执行聚类分析
        
        Args:
            embeddings: 嵌入向量
            method: 聚类方法 (kmeans, dbscan, hdbscan)
            n_clusters: 聚类数量（仅适用于kmeans）
        
        Returns:
            np.ndarray: 聚类标签
        """
        try:
            if embeddings.shape[0] == 0:
                return np.array([])
            
            if method.lower() == "kmeans":
                if n_clusters is None:
                    n_clusters = self.find_optimal_clusters(embeddings)
                
                self.cluster_model = KMeans(
                    n_clusters=n_clusters, 
                    random_state=42, 
                    n_init=10
                )
                self.cluster_labels = self.cluster_model.fit_predict(embeddings)
                self.cluster_centers = self.cluster_model.cluster_centers_
                
            elif method.lower() == "dbscan":
                # 自动估计eps参数
                from sklearn.neighbors import NearestNeighbors
                neighbors = NearestNeighbors(n_neighbors=5)
                neighbors_fit = neighbors.fit(embeddings)
                distances, indices = neighbors_fit.kneighbors(embeddings)
                distances = np.sort(distances, axis=0)
                distances = distances[:, 1]
                eps = np.percentile(distances, 90)
                
                self.cluster_model = DBSCAN(
                    eps=eps, 
                    min_samples=config.cluster.min_size
                )
                self.cluster_labels = self.cluster_model.fit_predict(embeddings)
                
            elif method.lower() == "hdbscan":
                self.cluster_model = hdbscan.HDBSCAN(
                    min_cluster_size=config.cluster.min_size,
                    min_samples=3,
                    cluster_selection_epsilon=0.0
                )
                self.cluster_labels = self.cluster_model.fit_predict(embeddings)
                
            else:
                raise ValueError(f"不支持的聚类方法: {method}")
            
            # 统计聚类结果
            unique_labels = np.unique(self.cluster_labels)
            n_clusters_found = len(unique_labels)
            if -1 in unique_labels:  # 噪声点
                n_clusters_found -= 1
            
            self.logger.info(f"聚类完成: 方法={method}, 聚类数={n_clusters_found}")
            return self.cluster_labels
            
        except Exception as e:
            self.logger.error(f"聚类分析失败: {e}")
            return np.zeros(embeddings.shape[0])
    
    def reduce_dimensions_for_visualization(self, embeddings: np.ndarray, 
                                          method: str = "umap") -> np.ndarray:
        """
        降维用于可视化
        
        Args:
            embeddings: 嵌入向量
            method: 降维方法 (umap, pca)
        
        Returns:
            np.ndarray: 2D降维结果
        """
        try:
            if method.lower() == "umap":
                self.umap_reducer = umap.UMAP(
                    n_components=2,
                    random_state=42,
                    n_neighbors=15,
                    min_dist=0.1
                )
                embeddings_2d = self.umap_reducer.fit_transform(embeddings)
                
            elif method.lower() == "pca":
                pca_2d = PCA(n_components=2, random_state=42)
                embeddings_2d = pca_2d.fit_transform(embeddings)
                
            else:
                raise ValueError(f"不支持的降维方法: {method}")
            
            return embeddings_2d
            
        except Exception as e:
            self.logger.error(f"降维失败: {e}")
            return embeddings[:, :2] if embeddings.shape[1] >= 2 else embeddings
    
    def analyze_clusters(self, conversations: List[Dict[str, Any]], 
                        cluster_labels: np.ndarray) -> Dict[int, Dict[str, Any]]:
        """
        分析聚类结果
        
        Args:
            conversations: 对话数据
            cluster_labels: 聚类标签
        
        Returns:
            Dict: 聚类分析结果
        """
        try:
            cluster_analysis = {}
            unique_labels = np.unique(cluster_labels)
            
            for label in unique_labels:
                if label == -1:  # 跳过噪声点
                    continue
                
                # 获取该聚类的对话
                cluster_mask = cluster_labels == label
                cluster_conversations = [conv for i, conv in enumerate(conversations) if cluster_mask[i]]
                
                if not cluster_conversations:
                    continue
                
                # 基本统计
                cluster_size = len(cluster_conversations)
                customer_ids = set(conv.get('customer_id', '') for conv in cluster_conversations)
                unique_customers = len(customer_ids)
                
                # 提取文本用于分析
                texts = [conv.get('text', '') for conv in cluster_conversations]
                
                # 生成话题描述
                keywords = self._extract_cluster_keywords(texts)
                topic_description = llm_client.generate_topic_description(
                    keywords, 
                    f"聚类大小: {cluster_size}, 客户数: {unique_customers}"
                )
                
                # 情感分析
                sentiment_analysis = self._analyze_cluster_sentiment(texts)
                
                cluster_analysis[int(label)] = {
                    'cluster_id': int(label),
                    'size': cluster_size,
                    'unique_customers': unique_customers,
                    'customer_ids': list(customer_ids),
                    'topic_description': topic_description,
                    'keywords': keywords,
                    'sentiment_analysis': sentiment_analysis,
                    'sample_conversations': texts[:5]  # 保存前5个样本
                }
            
            return cluster_analysis
            
        except Exception as e:
            self.logger.error(f"聚类分析失败: {e}")
            return {}
    
    def _extract_cluster_keywords(self, texts: List[str], top_k: int = 10) -> List[str]:
        """
        提取聚类关键词
        
        Args:
            texts: 文本列表
            top_k: 返回前k个关键词
        
        Returns:
            List[str]: 关键词列表
        """
        try:
            from collections import Counter
            import jieba
            import re
            
            # 合并所有文本
            combined_text = ' '.join(texts)
            
            # 中文分词
            words = jieba.lcut(combined_text)
            
            # 过滤停用词和短词
            filtered_words = []
            for word in words:
                word = word.strip()
                if (len(word) > 1 and 
                    not re.match(r'^[0-9\s\W]+$', word) and
                    word not in ['的', '了', '是', '在', '有', '和', '就', '不', '我', '你', '他']):
                    filtered_words.append(word)
            
            # 统计词频
            word_counts = Counter(filtered_words)
            keywords = [word for word, count in word_counts.most_common(top_k)]
            
            return keywords
            
        except Exception as e:
            self.logger.error(f"提取关键词失败: {e}")
            return []
    
    def _analyze_cluster_sentiment(self, texts: List[str]) -> Dict[str, Any]:
        """
        分析聚类情感
        
        Args:
            texts: 文本列表
        
        Returns:
            Dict: 情感分析结果
        """
        try:
            # 随机选择几个文本进行情感分析
            sample_texts = texts[:min(3, len(texts))]
            sentiment_results = []
            
            for text in sample_texts:
                result = llm_client.analyze_customer_sentiment(text)
                sentiment_results.append(result)
            
            # 汇总情感分析结果
            sentiments = [r.get('sentiment', '中性') for r in sentiment_results]
            sentiment_counter = {}
            for sentiment in sentiments:
                sentiment_counter[sentiment] = sentiment_counter.get(sentiment, 0) + 1
            
            # 主要情感
            main_sentiment = max(sentiment_counter, key=sentiment_counter.get) if sentiment_counter else '中性'
            
            return {
                'main_sentiment': main_sentiment,
                'sentiment_distribution': sentiment_counter,
                'sample_results': sentiment_results
            }
            
        except Exception as e:
            self.logger.error(f"聚类情感分析失败: {e}")
            return {'main_sentiment': '中性', 'sentiment_distribution': {}, 'sample_results': []}
    
    def generate_insights(self, cluster_analysis: Dict[int, Dict[str, Any]]) -> List[str]:
        """
        生成分析洞察
        
        Args:
            cluster_analysis: 聚类分析结果
        
        Returns:
            List[str]: 洞察列表
        """
        try:
            insights = llm_client.extract_key_insights(cluster_analysis)
            return insights
            
        except Exception as e:
            self.logger.error(f"生成洞察失败: {e}")
            return ["洞察生成失败"]
    
    def get_cluster_statistics(self) -> Dict[str, Any]:
        """
        获取聚类统计信息
        
        Returns:
            Dict: 统计信息
        """
        if self.cluster_labels is None:
            return {}
        
        unique_labels = np.unique(self.cluster_labels)
        n_clusters = len(unique_labels)
        if -1 in unique_labels:  # 排除噪声点
            n_clusters -= 1
        
        cluster_sizes = []
        for label in unique_labels:
            if label != -1:
                size = np.sum(self.cluster_labels == label)
                cluster_sizes.append(size)
        
        stats = {
            'n_clusters': n_clusters,
            'total_points': len(self.cluster_labels),
            'noise_points': np.sum(self.cluster_labels == -1),
            'avg_cluster_size': np.mean(cluster_sizes) if cluster_sizes else 0,
            'min_cluster_size': np.min(cluster_sizes) if cluster_sizes else 0,
            'max_cluster_size': np.max(cluster_sizes) if cluster_sizes else 0,
            'cluster_sizes': cluster_sizes
        }
        
        return stats


# 全局实例
customer_analyzer = CustomerAnalyzer()
