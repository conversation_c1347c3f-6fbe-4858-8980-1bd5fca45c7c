"""
话题发现模块
"""
import logging
import numpy as np
from typing import List, Dict, Any, Optional, Tuple
from collections import Counter, defaultdict
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import networkx as nx
from config.config import config
from src.langchain_app.llm_client import llm_client


class TopicDiscovery:
    """话题发现器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.tfidf_vectorizer = None
        self.topic_clusters = {}
        self.topic_graph = None
    
    def discover_topics_from_clusters(self, cluster_analysis: Dict[int, Dict[str, Any]]) -> Dict[str, Any]:
        """
        从聚类结果中发现话题
        
        Args:
            cluster_analysis: 聚类分析结果
        
        Returns:
            Dict: 话题发现结果
        """
        try:
            topics = {}
            topic_relationships = []
            
            for cluster_id, cluster_info in cluster_analysis.items():
                # 生成话题
                topic = self._create_topic_from_cluster(cluster_id, cluster_info)
                topics[f"topic_{cluster_id}"] = topic
                
                # 分析话题关系
                for other_cluster_id, other_cluster_info in cluster_analysis.items():
                    if cluster_id != other_cluster_id:
                        similarity = self._calculate_topic_similarity(
                            cluster_info, other_cluster_info
                        )
                        if similarity > config.cluster.similarity_threshold:
                            topic_relationships.append({
                                'topic1': f"topic_{cluster_id}",
                                'topic2': f"topic_{other_cluster_id}",
                                'similarity': similarity
                            })
            
            # 构建话题图
            self.topic_graph = self._build_topic_graph(topics, topic_relationships)
            
            # 识别主要话题
            main_topics = self._identify_main_topics(topics)
            
            # 生成话题摘要
            topic_summary = self._generate_topic_summary(topics, topic_relationships)
            
            result = {
                'topics': topics,
                'topic_relationships': topic_relationships,
                'main_topics': main_topics,
                'topic_summary': topic_summary,
                'topic_graph': self.topic_graph
            }
            
            self.logger.info(f"发现 {len(topics)} 个话题")
            return result
            
        except Exception as e:
            self.logger.error(f"话题发现失败: {e}")
            return {}
    
    def _create_topic_from_cluster(self, cluster_id: int, cluster_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        从聚类创建话题
        
        Args:
            cluster_id: 聚类ID
            cluster_info: 聚类信息
        
        Returns:
            Dict: 话题信息
        """
        try:
            # 基本信息
            topic = {
                'topic_id': f"topic_{cluster_id}",
                'cluster_id': cluster_id,
                'title': cluster_info.get('topic_description', f'话题 {cluster_id}'),
                'keywords': cluster_info.get('keywords', []),
                'size': cluster_info.get('size', 0),
                'unique_customers': cluster_info.get('unique_customers', 0),
                'customer_ids': cluster_info.get('customer_ids', []),
                'sentiment': cluster_info.get('sentiment_analysis', {}).get('main_sentiment', '中性'),
                'sample_conversations': cluster_info.get('sample_conversations', [])
            }
            
            # 计算话题重要性
            topic['importance'] = self._calculate_topic_importance(topic)
            
            # 生成详细描述
            topic['detailed_description'] = self._generate_detailed_description(topic)
            
            # 提取关键特征
            topic['features'] = self._extract_topic_features(topic)
            
            return topic
            
        except Exception as e:
            self.logger.error(f"创建话题失败: {e}")
            return {}
    
    def _calculate_topic_similarity(self, cluster1: Dict[str, Any], 
                                  cluster2: Dict[str, Any]) -> float:
        """
        计算话题相似度
        
        Args:
            cluster1: 聚类1信息
            cluster2: 聚类2信息
        
        Returns:
            float: 相似度分数
        """
        try:
            # 关键词相似度
            keywords1 = set(cluster1.get('keywords', []))
            keywords2 = set(cluster2.get('keywords', []))
            
            if not keywords1 or not keywords2:
                return 0.0
            
            # Jaccard相似度
            intersection = len(keywords1.intersection(keywords2))
            union = len(keywords1.union(keywords2))
            keyword_similarity = intersection / union if union > 0 else 0.0
            
            # 客户重叠度
            customers1 = set(cluster1.get('customer_ids', []))
            customers2 = set(cluster2.get('customer_ids', []))
            
            if customers1 and customers2:
                customer_overlap = len(customers1.intersection(customers2)) / len(customers1.union(customers2))
            else:
                customer_overlap = 0.0
            
            # 综合相似度
            similarity = 0.7 * keyword_similarity + 0.3 * customer_overlap
            
            return similarity
            
        except Exception as e:
            self.logger.error(f"计算话题相似度失败: {e}")
            return 0.0
    
    def _calculate_topic_importance(self, topic: Dict[str, Any]) -> float:
        """
        计算话题重要性
        
        Args:
            topic: 话题信息
        
        Returns:
            float: 重要性分数
        """
        try:
            # 基于大小、客户数量和情感的重要性计算
            size_score = min(topic.get('size', 0) / 100, 1.0)  # 标准化到0-1
            customer_score = min(topic.get('unique_customers', 0) / 50, 1.0)  # 标准化到0-1
            
            # 情感权重
            sentiment = topic.get('sentiment', '中性')
            sentiment_weight = {'正面': 1.2, '负面': 1.5, '中性': 1.0}.get(sentiment, 1.0)
            
            importance = (0.4 * size_score + 0.4 * customer_score + 0.2) * sentiment_weight
            
            return min(importance, 1.0)
            
        except Exception as e:
            self.logger.error(f"计算话题重要性失败: {e}")
            return 0.5
    
    def _generate_detailed_description(self, topic: Dict[str, Any]) -> str:
        """
        生成详细话题描述
        
        Args:
            topic: 话题信息
        
        Returns:
            str: 详细描述
        """
        try:
            context = f"""
话题基本信息：
- 话题标题：{topic.get('title', '')}
- 关键词：{', '.join(topic.get('keywords', []))}
- 涉及客户数：{topic.get('unique_customers', 0)}
- 对话数量：{topic.get('size', 0)}
- 主要情感：{topic.get('sentiment', '中性')}

样本对话：
{chr(10).join(topic.get('sample_conversations', [])[:3])}
"""
            
            detailed_description = llm_client.generate_text(
                "请基于以上信息，生成一个详细的话题描述，包括话题的主要内容、客户关注点和业务意义。",
                "你是一个专业的客户对话分析师，请生成简洁但全面的话题描述。"
            )
            
            return detailed_description or topic.get('title', '')
            
        except Exception as e:
            self.logger.error(f"生成详细描述失败: {e}")
            return topic.get('title', '')
    
    def _extract_topic_features(self, topic: Dict[str, Any]) -> Dict[str, Any]:
        """
        提取话题特征
        
        Args:
            topic: 话题信息
        
        Returns:
            Dict: 话题特征
        """
        try:
            features = {
                'keyword_count': len(topic.get('keywords', [])),
                'avg_conversation_length': 0,
                'customer_diversity': topic.get('unique_customers', 0) / max(topic.get('size', 1), 1),
                'sentiment_polarity': topic.get('sentiment', '中性'),
                'topic_density': topic.get('size', 0) / max(topic.get('unique_customers', 1), 1)
            }
            
            # 计算平均对话长度
            conversations = topic.get('sample_conversations', [])
            if conversations:
                total_length = sum(len(conv.split()) for conv in conversations)
                features['avg_conversation_length'] = total_length / len(conversations)
            
            return features
            
        except Exception as e:
            self.logger.error(f"提取话题特征失败: {e}")
            return {}
    
    def _build_topic_graph(self, topics: Dict[str, Any], 
                          relationships: List[Dict[str, Any]]) -> nx.Graph:
        """
        构建话题关系图
        
        Args:
            topics: 话题字典
            relationships: 话题关系列表
        
        Returns:
            nx.Graph: 话题图
        """
        try:
            G = nx.Graph()
            
            # 添加节点
            for topic_id, topic_info in topics.items():
                G.add_node(topic_id, **topic_info)
            
            # 添加边
            for rel in relationships:
                G.add_edge(
                    rel['topic1'], 
                    rel['topic2'], 
                    weight=rel['similarity']
                )
            
            return G
            
        except Exception as e:
            self.logger.error(f"构建话题图失败: {e}")
            return nx.Graph()
    
    def _identify_main_topics(self, topics: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        识别主要话题
        
        Args:
            topics: 话题字典
        
        Returns:
            List[Dict]: 主要话题列表
        """
        try:
            # 按重要性排序
            sorted_topics = sorted(
                topics.values(),
                key=lambda x: x.get('importance', 0),
                reverse=True
            )
            
            # 选择前5个最重要的话题
            main_topics = sorted_topics[:5]
            
            return main_topics
            
        except Exception as e:
            self.logger.error(f"识别主要话题失败: {e}")
            return []
    
    def _generate_topic_summary(self, topics: Dict[str, Any], 
                               relationships: List[Dict[str, Any]]) -> str:
        """
        生成话题摘要
        
        Args:
            topics: 话题字典
            relationships: 话题关系列表
        
        Returns:
            str: 话题摘要
        """
        try:
            summary_data = {
                'total_topics': len(topics),
                'total_relationships': len(relationships),
                'main_topics': [t.get('title', '') for t in self._identify_main_topics(topics)],
                'sentiment_distribution': self._get_sentiment_distribution(topics)
            }
            
            summary = llm_client.generate_text(
                f"请基于以下数据生成话题分析摘要：{summary_data}",
                "你是一个专业的数据分析师，请生成简洁的话题分析摘要。"
            )
            
            return summary or "话题分析摘要生成失败"
            
        except Exception as e:
            self.logger.error(f"生成话题摘要失败: {e}")
            return "话题摘要生成失败"
    
    def _get_sentiment_distribution(self, topics: Dict[str, Any]) -> Dict[str, int]:
        """
        获取情感分布
        
        Args:
            topics: 话题字典
        
        Returns:
            Dict: 情感分布
        """
        sentiment_counts = Counter()
        for topic in topics.values():
            sentiment = topic.get('sentiment', '中性')
            sentiment_counts[sentiment] += 1
        
        return dict(sentiment_counts)
    
    def find_emerging_topics(self, current_topics: Dict[str, Any], 
                           historical_topics: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        发现新兴话题
        
        Args:
            current_topics: 当前话题
            historical_topics: 历史话题
        
        Returns:
            List[Dict]: 新兴话题列表
        """
        try:
            emerging_topics = []
            
            current_keywords = set()
            for topic in current_topics.values():
                current_keywords.update(topic.get('keywords', []))
            
            historical_keywords = set()
            for topic in historical_topics.values():
                historical_keywords.update(topic.get('keywords', []))
            
            # 找出新关键词
            new_keywords = current_keywords - historical_keywords
            
            # 识别包含新关键词的话题
            for topic_id, topic in current_topics.items():
                topic_keywords = set(topic.get('keywords', []))
                if topic_keywords.intersection(new_keywords):
                    emerging_topics.append({
                        'topic_id': topic_id,
                        'title': topic.get('title', ''),
                        'new_keywords': list(topic_keywords.intersection(new_keywords)),
                        'importance': topic.get('importance', 0)
                    })
            
            # 按重要性排序
            emerging_topics.sort(key=lambda x: x['importance'], reverse=True)
            
            return emerging_topics
            
        except Exception as e:
            self.logger.error(f"发现新兴话题失败: {e}")
            return []
    
    def get_topic_evolution(self, topic_history: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        分析话题演化
        
        Args:
            topic_history: 话题历史数据
        
        Returns:
            Dict: 话题演化分析
        """
        try:
            if len(topic_history) < 2:
                return {'evolution': '数据不足，无法分析话题演化'}
            
            evolution_analysis = {
                'trend_analysis': self._analyze_topic_trends(topic_history),
                'keyword_evolution': self._analyze_keyword_evolution(topic_history),
                'sentiment_evolution': self._analyze_sentiment_evolution(topic_history)
            }
            
            return evolution_analysis
            
        except Exception as e:
            self.logger.error(f"分析话题演化失败: {e}")
            return {'evolution': '话题演化分析失败'}
    
    def _analyze_topic_trends(self, topic_history: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析话题趋势"""
        # 简化实现，实际应用中可以更复杂
        return {
            'total_topics_trend': [len(topics.get('topics', {})) for topics in topic_history],
            'avg_importance_trend': [
                np.mean([t.get('importance', 0) for t in topics.get('topics', {}).values()])
                for topics in topic_history
            ]
        }
    
    def _analyze_keyword_evolution(self, topic_history: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析关键词演化"""
        all_keywords = []
        for topics in topic_history:
            period_keywords = set()
            for topic in topics.get('topics', {}).values():
                period_keywords.update(topic.get('keywords', []))
            all_keywords.append(period_keywords)
        
        return {
            'keyword_count_trend': [len(keywords) for keywords in all_keywords],
            'new_keywords_per_period': [
                len(all_keywords[i] - all_keywords[i-1]) if i > 0 else 0
                for i in range(len(all_keywords))
            ]
        }
    
    def _analyze_sentiment_evolution(self, topic_history: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析情感演化"""
        sentiment_trends = {'正面': [], '负面': [], '中性': []}
        
        for topics in topic_history:
            sentiment_dist = self._get_sentiment_distribution(topics.get('topics', {}))
            for sentiment in sentiment_trends:
                sentiment_trends[sentiment].append(sentiment_dist.get(sentiment, 0))
        
        return sentiment_trends


# 全局实例
topic_discovery = TopicDiscovery()
