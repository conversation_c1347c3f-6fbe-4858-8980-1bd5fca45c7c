"""
文本向量化模块
"""
import logging
from typing import List, Union, Optional
import numpy as np
from sentence_transformers import SentenceTransformer
from config.config import config


class TextEmbedder:
    """文本向量化器"""
    
    def __init__(self, model_name: Optional[str] = None):
        """
        初始化文本向量化器
        
        Args:
            model_name: 模型名称，如果为None则使用配置文件中的模型
        """
        self.logger = logging.getLogger(__name__)
        self.model_name = model_name or config.embedding.model
        self.model = None
        self._load_model()
    
    def _load_model(self) -> bool:
        """
        加载向量化模型
        
        Returns:
            bool: 加载是否成功
        """
        try:
            self.model = SentenceTransformer(self.model_name)
            self.logger.info(f"成功加载向量化模型: {self.model_name}")
            return True
        except Exception as e:
            self.logger.error(f"加载向量化模型失败: {e}")
            return False
    
    def encode_texts(self, texts: List[str], batch_size: Optional[int] = None) -> np.ndarray:
        """
        将文本列表转换为向量
        
        Args:
            texts: 文本列表
            batch_size: 批处理大小
        
        Returns:
            np.ndarray: 向量数组
        """
        try:
            if not self.model:
                self.logger.error("模型未加载")
                return np.array([])
            
            if not texts:
                self.logger.warning("输入文本列表为空")
                return np.array([])
            
            batch_size = batch_size or config.data_processing.batch_size
            
            # 文本预处理
            processed_texts = self._preprocess_texts(texts)
            
            # 生成向量
            embeddings = self.model.encode(
                processed_texts,
                batch_size=batch_size,
                show_progress_bar=True,
                convert_to_numpy=True
            )
            
            self.logger.info(f"成功生成 {len(texts)} 个文本的向量，维度: {embeddings.shape}")
            return embeddings
            
        except Exception as e:
            self.logger.error(f"文本向量化失败: {e}")
            return np.array([])
    
    def encode_single_text(self, text: str) -> np.ndarray:
        """
        将单个文本转换为向量
        
        Args:
            text: 输入文本
        
        Returns:
            np.ndarray: 向量
        """
        try:
            if not self.model:
                self.logger.error("模型未加载")
                return np.array([])
            
            if not text or not text.strip():
                self.logger.warning("输入文本为空")
                return np.array([])
            
            # 文本预处理
            processed_text = self._preprocess_text(text)
            
            # 生成向量
            embedding = self.model.encode(processed_text, convert_to_numpy=True)
            
            return embedding
            
        except Exception as e:
            self.logger.error(f"单个文本向量化失败: {e}")
            return np.array([])
    
    def _preprocess_texts(self, texts: List[str]) -> List[str]:
        """
        批量预处理文本
        
        Args:
            texts: 文本列表
        
        Returns:
            List[str]: 预处理后的文本列表
        """
        processed_texts = []
        for text in texts:
            processed_text = self._preprocess_text(text)
            if processed_text:  # 只添加非空文本
                processed_texts.append(processed_text)
        
        return processed_texts
    
    def _preprocess_text(self, text: str) -> str:
        """
        预处理单个文本
        
        Args:
            text: 输入文本
        
        Returns:
            str: 预处理后的文本
        """
        if not text:
            return ""
        
        # 基本清理
        text = text.strip()
        
        # 长度检查
        if len(text) < config.data_processing.min_text_length:
            return ""
        
        # 截断过长文本
        if len(text) > config.data_processing.max_text_length:
            text = text[:config.data_processing.max_text_length]
        
        return text
    
    def calculate_similarity(self, embeddings1: np.ndarray, embeddings2: np.ndarray) -> np.ndarray:
        """
        计算向量相似度
        
        Args:
            embeddings1: 第一组向量
            embeddings2: 第二组向量
        
        Returns:
            np.ndarray: 相似度矩阵
        """
        try:
            if not self.model:
                self.logger.error("模型未加载")
                return np.array([])
            
            similarities = self.model.similarity(embeddings1, embeddings2)
            return similarities.numpy() if hasattr(similarities, 'numpy') else similarities
            
        except Exception as e:
            self.logger.error(f"计算相似度失败: {e}")
            return np.array([])
    
    def get_model_info(self) -> dict:
        """
        获取模型信息
        
        Returns:
            dict: 模型信息
        """
        if not self.model:
            return {}
        
        try:
            info = {
                "model_name": self.model_name,
                "max_seq_length": getattr(self.model, 'max_seq_length', 'Unknown'),
                "embedding_dimension": config.embedding.dimension,
                "device": str(getattr(self.model, 'device', 'Unknown'))
            }
            return info
        except Exception as e:
            self.logger.error(f"获取模型信息失败: {e}")
            return {}
    
    def set_max_seq_length(self, max_length: int):
        """
        设置最大序列长度
        
        Args:
            max_length: 最大序列长度
        """
        try:
            if self.model:
                self.model.max_seq_length = max_length
                self.logger.info(f"设置最大序列长度为: {max_length}")
        except Exception as e:
            self.logger.error(f"设置最大序列长度失败: {e}")


# 全局实例
text_embedder = TextEmbedder()
