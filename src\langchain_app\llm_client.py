"""
本地大模型客户端
"""
import logging
from typing import Optional, List, Dict, Any
from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage, SystemMessage
from langchain_core.prompts import PromptTemplate
from langchain_core.output_parsers import StrOutputParser
from config.config import config


class LocalLLMClient:
    """本地大模型客户端"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.llm = None
        self._initialize_llm()
    
    def _initialize_llm(self) -> bool:
        """
        初始化本地大模型
        
        Returns:
            bool: 初始化是否成功
        """
        try:
            # 使用OpenAI兼容的API连接本地模型
            self.llm = ChatOpenAI(
                model=config.llm.model,
                openai_api_key=config.llm.api_key,
                openai_api_base=config.llm.endpoint,
                temperature=config.llm.temperature,
                max_tokens=config.llm.max_tokens,
                timeout=60,
                max_retries=3
            )
            
            self.logger.info(f"成功初始化本地大模型: {config.llm.model}")
            return True
            
        except Exception as e:
            self.logger.error(f"初始化本地大模型失败: {e}")
            return False
    
    def generate_text(self, prompt: str, system_message: Optional[str] = None) -> str:
        """
        生成文本
        
        Args:
            prompt: 用户提示
            system_message: 系统消息
        
        Returns:
            str: 生成的文本
        """
        try:
            if not self.llm:
                self.logger.error("大模型未初始化")
                return ""
            
            messages = []
            
            # 添加系统消息
            if system_message:
                messages.append(SystemMessage(content=system_message))
            
            # 添加用户消息
            messages.append(HumanMessage(content=prompt))
            
            # 生成回复
            response = self.llm.invoke(messages)
            
            return response.content if hasattr(response, 'content') else str(response)
            
        except Exception as e:
            self.logger.error(f"文本生成失败: {e}")
            return ""
    
    def generate_topic_description(self, keywords: List[str], context: str = "") -> str:
        """
        生成话题描述
        
        Args:
            keywords: 关键词列表
            context: 上下文信息
        
        Returns:
            str: 话题描述
        """
        try:
            system_message = """你是一个专业的客户对话分析师。请根据提供的关键词和上下文，生成一个简洁、准确的话题描述。
            
要求：
1. 描述应该简洁明了，不超过100字
2. 准确概括关键词所代表的主题
3. 使用专业但易懂的语言
4. 如果有上下文信息，请结合上下文进行分析"""
            
            keywords_str = "、".join(keywords)
            prompt = f"""关键词：{keywords_str}

上下文信息：{context}

请生成一个话题描述："""
            
            return self.generate_text(prompt, system_message)
            
        except Exception as e:
            self.logger.error(f"生成话题描述失败: {e}")
            return f"话题：{', '.join(keywords[:3])}"
    
    def analyze_customer_sentiment(self, text: str) -> Dict[str, Any]:
        """
        分析客户情感
        
        Args:
            text: 客户对话文本
        
        Returns:
            Dict: 情感分析结果
        """
        try:
            system_message = """你是一个专业的情感分析师。请分析客户对话的情感倾向。

请按照以下格式返回结果：
情感类型：[正面/负面/中性]
情感强度：[强/中/弱]
关键情感词：[词1, 词2, 词3]
分析说明：[简要说明]"""
            
            prompt = f"请分析以下客户对话的情感：\n\n{text}"
            
            response = self.generate_text(prompt, system_message)
            
            # 简单解析返回结果
            result = {
                "sentiment": "中性",
                "intensity": "中",
                "keywords": [],
                "explanation": response
            }
            
            # 尝试从回复中提取结构化信息
            lines = response.split('\n')
            for line in lines:
                if '情感类型：' in line:
                    sentiment = line.split('：')[1].strip()
                    if '正面' in sentiment:
                        result["sentiment"] = "正面"
                    elif '负面' in sentiment:
                        result["sentiment"] = "负面"
                elif '情感强度：' in line:
                    intensity = line.split('：')[1].strip()
                    result["intensity"] = intensity
            
            return result
            
        except Exception as e:
            self.logger.error(f"情感分析失败: {e}")
            return {
                "sentiment": "中性",
                "intensity": "中",
                "keywords": [],
                "explanation": "分析失败"
            }
    
    def summarize_conversations(self, conversations: List[str]) -> str:
        """
        总结对话内容
        
        Args:
            conversations: 对话列表
        
        Returns:
            str: 总结内容
        """
        try:
            system_message = """你是一个专业的对话分析师。请对提供的客户对话进行总结。

要求：
1. 提取主要话题和关键信息
2. 识别客户的主要需求和关注点
3. 总结长度控制在200字以内
4. 使用客观、专业的语言"""
            
            conversations_text = "\n\n".join([f"对话{i+1}: {conv}" for i, conv in enumerate(conversations)])
            
            prompt = f"请总结以下客户对话：\n\n{conversations_text}"
            
            return self.generate_text(prompt, system_message)
            
        except Exception as e:
            self.logger.error(f"对话总结失败: {e}")
            return "总结生成失败"
    
    def extract_key_insights(self, analysis_data: Dict[str, Any]) -> List[str]:
        """
        提取关键洞察
        
        Args:
            analysis_data: 分析数据
        
        Returns:
            List[str]: 关键洞察列表
        """
        try:
            system_message = """你是一个专业的数据分析师。请根据客户对话分析数据，提取关键洞察。

要求：
1. 每个洞察应该简洁明了
2. 重点关注客户行为模式和需求
3. 提供可操作的建议
4. 最多提供5个关键洞察"""
            
            data_summary = str(analysis_data)
            prompt = f"请根据以下分析数据提取关键洞察：\n\n{data_summary}"
            
            response = self.generate_text(prompt, system_message)
            
            # 简单解析洞察列表
            insights = []
            lines = response.split('\n')
            for line in lines:
                line = line.strip()
                if line and (line.startswith('1.') or line.startswith('2.') or 
                           line.startswith('3.') or line.startswith('4.') or 
                           line.startswith('5.') or line.startswith('-') or 
                           line.startswith('•')):
                    # 清理格式
                    insight = line.lstrip('12345.-•').strip()
                    if insight:
                        insights.append(insight)
            
            return insights[:5]  # 最多返回5个洞察
            
        except Exception as e:
            self.logger.error(f"提取关键洞察失败: {e}")
            return ["洞察提取失败"]
    
    def create_chain(self, template: str) -> Any:
        """
        创建LangChain链
        
        Args:
            template: 提示模板
        
        Returns:
            LangChain链对象
        """
        try:
            if not self.llm:
                self.logger.error("大模型未初始化")
                return None
            
            prompt = PromptTemplate.from_template(template)
            chain = prompt | self.llm | StrOutputParser()
            
            return chain
            
        except Exception as e:
            self.logger.error(f"创建链失败: {e}")
            return None
    
    def test_connection(self) -> bool:
        """
        测试连接
        
        Returns:
            bool: 连接是否正常
        """
        try:
            test_response = self.generate_text("你好，请回复'连接正常'")
            return "连接正常" in test_response or len(test_response) > 0
        except Exception as e:
            self.logger.error(f"连接测试失败: {e}")
            return False
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        获取模型信息
        
        Returns:
            Dict: 模型信息
        """
        return {
            "model_name": config.llm.model,
            "endpoint": config.llm.endpoint,
            "temperature": config.llm.temperature,
            "max_tokens": config.llm.max_tokens,
            "is_initialized": self.llm is not None
        }


# 全局实例
llm_client = LocalLLMClient()
