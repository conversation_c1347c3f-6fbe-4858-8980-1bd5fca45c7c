"""
客户对话分析流水线配置模块
"""
import os
from typing import Optional
from pydantic import BaseSettings, Field
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()


class LLMConfig(BaseSettings):
    """大模型配置"""
    endpoint: str = Field(default="http://************:9103/v1", env="LLM_ENDPOINT")
    model: str = Field(default="Qwen3-4B", env="LLM_MODEL")
    api_key: str = Field(default="your_api_key_here", env="LLM_API_KEY")
    temperature: float = Field(default=0.7, env="LLM_TEMPERATURE")
    max_tokens: int = Field(default=2048, env="LLM_MAX_TOKENS")


class MilvusConfig(BaseSettings):
    """Milvus向量数据库配置"""
    host: str = Field(default="localhost", env="MILVUS_HOST")
    port: int = Field(default=19530, env="MILVUS_PORT")
    user: Optional[str] = Field(default=None, env="MILVUS_USER")
    password: Optional[str] = Field(default=None, env="MILVUS_PASSWORD")
    database: str = Field(default="customer_analysis", env="MILVUS_DATABASE")
    collection_name: str = Field(default="customer_conversations", env="MILVUS_COLLECTION_NAME")


class EmbeddingConfig(BaseSettings):
    """向量化配置"""
    model: str = Field(default="sentence-transformers/all-MiniLM-L6-v2", env="EMBEDDING_MODEL")
    dimension: int = Field(default=384, env="EMBEDDING_DIMENSION")
    index_type: str = Field(default="IVF_FLAT", env="VECTOR_INDEX_TYPE")
    metric_type: str = Field(default="L2", env="VECTOR_METRIC_TYPE")


class DataProcessingConfig(BaseSettings):
    """数据处理配置"""
    batch_size: int = Field(default=100, env="BATCH_SIZE")
    max_text_length: int = Field(default=512, env="MAX_TEXT_LENGTH")
    min_text_length: int = Field(default=10, env="MIN_TEXT_LENGTH")


class ClusterConfig(BaseSettings):
    """聚类分析配置"""
    min_size: int = Field(default=5, env="CLUSTER_MIN_SIZE")
    max_size: int = Field(default=100, env="CLUSTER_MAX_SIZE")
    similarity_threshold: float = Field(default=0.8, env="SIMILARITY_THRESHOLD")
    topic_description_length: int = Field(default=100, env="TOPIC_DESCRIPTION_LENGTH")


class AppConfig(BaseSettings):
    """应用配置"""
    name: str = Field(default="Customer Conversation Analysis Pipeline", env="APP_NAME")
    version: str = Field(default="1.0.0", env="APP_VERSION")
    debug: bool = Field(default=True, env="DEBUG")
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    log_file: str = Field(default="logs/customer_analysis.log", env="LOG_FILE")


class Config:
    """主配置类"""
    def __init__(self):
        self.llm = LLMConfig()
        self.milvus = MilvusConfig()
        self.embedding = EmbeddingConfig()
        self.data_processing = DataProcessingConfig()
        self.cluster = ClusterConfig()
        self.app = AppConfig()


# 全局配置实例
config = Config()
