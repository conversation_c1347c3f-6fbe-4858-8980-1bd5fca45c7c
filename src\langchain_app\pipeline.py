"""
客户对话分析流水线
"""
import logging
import time
from typing import List, Dict, Any, Optional
import numpy as np
from pathlib import Path

from config.config import config
from src.vector_db.milvus_client import MilvusClient
from src.data_processing.preprocessor import ConversationPreprocessor
from src.data_processing.text_embedder import TextEmbedder
from src.analysis.customer_analyzer import CustomerAnalyzer
from src.analysis.topic_discovery import TopicDiscovery
from src.langchain_app.llm_client import LocalLLMClient


class CustomerAnalysisPipeline:
    """客户对话分析流水线"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 初始化组件
        self.milvus_client = MilvusClient()
        self.preprocessor = ConversationPreprocessor()
        self.text_embedder = TextEmbedder()
        self.customer_analyzer = CustomerAnalyzer()
        self.topic_discovery = TopicDiscovery()
        self.llm_client = LocalLLMClient()
        
        # 状态跟踪
        self.is_initialized = False
        self.last_analysis_time = None
        self.analysis_results = {}
    
    def initialize(self) -> bool:
        """
        初始化流水线
        
        Returns:
            bool: 初始化是否成功
        """
        try:
            self.logger.info("开始初始化客户对话分析流水线...")
            
            # 测试LLM连接
            if not self.llm_client.test_connection():
                self.logger.error("LLM连接测试失败")
                return False
            
            # 初始化Milvus
            if not self.milvus_client.connect():
                self.logger.error("Milvus连接失败")
                return False
            
            if not self.milvus_client.create_collection():
                self.logger.error("创建Milvus集合失败")
                return False
            
            if not self.milvus_client.create_index():
                self.logger.error("创建Milvus索引失败")
                return False
            
            if not self.milvus_client.load_collection():
                self.logger.error("加载Milvus集合失败")
                return False
            
            self.is_initialized = True
            self.logger.info("流水线初始化成功")
            return True
            
        except Exception as e:
            self.logger.error(f"流水线初始化失败: {e}")
            return False
    
    def process_conversation_data(self, file_path: str, file_format: str = "json") -> bool:
        """
        处理对话数据
        
        Args:
            file_path: 数据文件路径
            file_format: 文件格式
        
        Returns:
            bool: 处理是否成功
        """
        try:
            if not self.is_initialized:
                self.logger.error("流水线未初始化")
                return False
            
            self.logger.info(f"开始处理对话数据: {file_path}")
            
            # 1. 加载数据
            conversations = self.preprocessor.load_conversation_data(file_path, file_format)
            if not conversations:
                self.logger.error("加载对话数据失败")
                return False
            
            self.logger.info(f"加载了 {len(conversations)} 个对话")
            
            # 2. 数据验证和预处理
            valid_conversations, errors = self.preprocessor.validate_conversation_data(conversations)
            if errors:
                self.logger.warning(f"数据验证发现 {len(errors)} 个错误")
            
            processed_conversations = self.preprocessor.preprocess_conversations(valid_conversations)
            if not processed_conversations:
                self.logger.error("预处理对话数据失败")
                return False
            
            # 3. 生成向量嵌入
            texts = [conv['text'] for conv in processed_conversations]
            embeddings = self.text_embedder.encode_texts(texts)
            if embeddings.size == 0:
                self.logger.error("生成文本嵌入失败")
                return False
            
            # 4. 存储到向量数据库
            if not self._store_to_milvus(processed_conversations, embeddings):
                self.logger.error("存储到Milvus失败")
                return False
            
            self.logger.info("对话数据处理完成")
            return True
            
        except Exception as e:
            self.logger.error(f"处理对话数据失败: {e}")
            return False
    
    def run_analysis(self, clustering_method: str = "kmeans", 
                    n_clusters: Optional[int] = None) -> Dict[str, Any]:
        """
        运行客户群体分析
        
        Args:
            clustering_method: 聚类方法
            n_clusters: 聚类数量
        
        Returns:
            Dict: 分析结果
        """
        try:
            if not self.is_initialized:
                self.logger.error("流水线未初始化")
                return {}
            
            self.logger.info("开始运行客户群体分析...")
            start_time = time.time()
            
            # 1. 从Milvus获取数据
            conversations, embeddings = self._retrieve_from_milvus()
            if not conversations or embeddings.size == 0:
                self.logger.error("从Milvus获取数据失败")
                return {}
            
            self.logger.info(f"获取了 {len(conversations)} 个对话和 {embeddings.shape} 的嵌入向量")
            
            # 2. 预处理嵌入向量
            processed_embeddings = self.customer_analyzer.preprocess_embeddings(embeddings)
            
            # 3. 执行聚类分析
            cluster_labels = self.customer_analyzer.perform_clustering(
                processed_embeddings, 
                method=clustering_method,
                n_clusters=n_clusters
            )
            
            # 4. 分析聚类结果
            cluster_analysis = self.customer_analyzer.analyze_clusters(conversations, cluster_labels)
            
            # 5. 话题发现
            topic_results = self.topic_discovery.discover_topics_from_clusters(cluster_analysis)
            
            # 6. 生成洞察
            insights = self.customer_analyzer.generate_insights(cluster_analysis)
            
            # 7. 降维用于可视化
            embeddings_2d = self.customer_analyzer.reduce_dimensions_for_visualization(processed_embeddings)
            
            # 8. 获取统计信息
            cluster_stats = self.customer_analyzer.get_cluster_statistics()
            
            # 9. 生成综合报告
            analysis_report = self._generate_analysis_report(
                cluster_analysis, topic_results, insights, cluster_stats
            )
            
            # 组装结果
            results = {
                'timestamp': time.time(),
                'processing_time': time.time() - start_time,
                'data_summary': {
                    'total_conversations': len(conversations),
                    'total_customers': len(set(conv.get('customer_id', '') for conv in conversations)),
                    'embedding_dimension': embeddings.shape[1] if len(embeddings.shape) > 1 else 0
                },
                'cluster_analysis': cluster_analysis,
                'topic_results': topic_results,
                'insights': insights,
                'cluster_statistics': cluster_stats,
                'visualization_data': {
                    'embeddings_2d': embeddings_2d.tolist() if embeddings_2d.size > 0 else [],
                    'cluster_labels': cluster_labels.tolist() if cluster_labels.size > 0 else []
                },
                'analysis_report': analysis_report
            }
            
            self.analysis_results = results
            self.last_analysis_time = time.time()
            
            self.logger.info(f"分析完成，耗时 {results['processing_time']:.2f} 秒")
            return results
            
        except Exception as e:
            self.logger.error(f"运行分析失败: {e}")
            return {}
    
    def _store_to_milvus(self, conversations: List[Dict[str, Any]], 
                        embeddings: np.ndarray) -> bool:
        """
        存储数据到Milvus
        
        Args:
            conversations: 对话数据
            embeddings: 嵌入向量
        
        Returns:
            bool: 存储是否成功
        """
        try:
            # 准备数据
            conversation_ids = [conv['conversation_id'] for conv in conversations]
            customer_ids = [conv['customer_id'] for conv in conversations]
            texts = [conv['text'] for conv in conversations]
            timestamps = [conv['timestamp'] for conv in conversations]
            
            # 转换为Milvus格式
            data = [
                conversation_ids,
                customer_ids,
                texts,
                timestamps,
                embeddings.tolist()
            ]
            
            return self.milvus_client.insert_data(data)
            
        except Exception as e:
            self.logger.error(f"存储到Milvus失败: {e}")
            return False
    
    def _retrieve_from_milvus(self) -> tuple:
        """
        从Milvus检索数据
        
        Returns:
            tuple: (对话数据, 嵌入向量)
        """
        try:
            # 这里简化实现，实际应用中需要更复杂的查询逻辑
            # 由于Milvus主要用于向量搜索，我们需要另外的方式来获取所有数据
            # 这里返回空数据，实际实现中需要根据具体需求调整
            
            self.logger.warning("从Milvus检索所有数据的功能需要进一步实现")
            return [], np.array([])
            
        except Exception as e:
            self.logger.error(f"从Milvus检索数据失败: {e}")
            return [], np.array([])
    
    def _generate_analysis_report(self, cluster_analysis: Dict[int, Dict[str, Any]],
                                topic_results: Dict[str, Any],
                                insights: List[str],
                                cluster_stats: Dict[str, Any]) -> str:
        """
        生成分析报告
        
        Args:
            cluster_analysis: 聚类分析结果
            topic_results: 话题发现结果
            insights: 洞察列表
            cluster_stats: 聚类统计
        
        Returns:
            str: 分析报告
        """
        try:
            report_data = {
                'cluster_count': cluster_stats.get('n_clusters', 0),
                'total_conversations': cluster_stats.get('total_points', 0),
                'main_topics': [t.get('title', '') for t in topic_results.get('main_topics', [])],
                'key_insights': insights[:3],  # 前3个关键洞察
                'sentiment_distribution': topic_results.get('topic_summary', '')
            }
            
            report = self.llm_client.generate_text(
                f"请基于以下分析数据生成一份客户对话分析报告：{report_data}",
                "你是一个专业的客户分析师，请生成一份简洁但全面的分析报告，包括主要发现、关键洞察和建议。"
            )
            
            return report or "分析报告生成失败"
            
        except Exception as e:
            self.logger.error(f"生成分析报告失败: {e}")
            return "分析报告生成失败"
    
    def search_similar_conversations(self, query_text: str, top_k: int = 10) -> List[Dict[str, Any]]:
        """
        搜索相似对话
        
        Args:
            query_text: 查询文本
            top_k: 返回结果数量
        
        Returns:
            List[Dict]: 相似对话列表
        """
        try:
            if not self.is_initialized:
                self.logger.error("流水线未初始化")
                return []
            
            # 生成查询向量
            query_embedding = self.text_embedder.encode_single_text(query_text)
            if query_embedding.size == 0:
                self.logger.error("生成查询向量失败")
                return []
            
            # 在Milvus中搜索
            results = self.milvus_client.search([query_embedding.tolist()], limit=top_k)
            
            return results
            
        except Exception as e:
            self.logger.error(f"搜索相似对话失败: {e}")
            return []
    
    def get_pipeline_status(self) -> Dict[str, Any]:
        """
        获取流水线状态
        
        Returns:
            Dict: 状态信息
        """
        return {
            'is_initialized': self.is_initialized,
            'last_analysis_time': self.last_analysis_time,
            'milvus_stats': self.milvus_client.get_collection_stats(),
            'llm_info': self.llm_client.get_model_info(),
            'embedder_info': self.text_embedder.get_model_info()
        }
    
    def cleanup(self):
        """清理资源"""
        try:
            if self.milvus_client:
                self.milvus_client.disconnect()
            self.logger.info("流水线资源清理完成")
        except Exception as e:
            self.logger.error(f"清理资源失败: {e}")


# 全局实例
pipeline = CustomerAnalysisPipeline()
