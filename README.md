# 客户对话分析流水线

基于Milvus向量数据库和LangChain的客户对话分析系统，实现客户群体分析和话题发现功能。

## 项目概述

本项目构建了一个完整的客户对话分析流水线，能够：

- 📊 **数据处理**: 自动加载、清洗和预处理客户对话数据
- 🔍 **向量化**: 使用SentenceTransformers将文本转换为高质量向量表示
- 🗄️ **向量存储**: 基于Milvus构建高性能向量数据库
- 🎯 **聚类分析**: 使用多种聚类算法发现客户群体模式
- 💡 **话题发现**: 自动识别对话中的主要话题和趋势
- 🤖 **智能分析**: 集成本地大模型生成洞察和报告

## 技术架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   数据输入      │    │   文本处理      │    │   向量化        │
│  JSON/CSV/Excel │───▶│  清洗+标准化    │───▶│ SentenceTransf. │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   分析报告      │    │   聚类分析      │    │   向量存储      │
│  洞察+可视化    │◀───│  KMeans/DBSCAN  │◀───│    Milvus       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         ▲                       │
         │              ┌─────────────────┐
         │              │   话题发现      │
         └──────────────│  LangChain+LLM  │
                        └─────────────────┘
```

## 核心功能

### 1. 数据处理模块
- **多格式支持**: JSON、CSV、Excel文件导入
- **数据清洗**: 自动去除噪声、标准化格式
- **数据验证**: 完整性检查和错误报告
- **批处理**: 支持大规模数据分批处理

### 2. 向量化模块
- **预训练模型**: 使用all-MiniLM-L6-v2等高质量模型
- **中英文支持**: 自动语言检测和处理
- **批量编码**: 高效的批量文本向量化
- **相似度计算**: 多种相似度度量方法

### 3. 向量数据库
- **Milvus集成**: 高性能向量存储和检索
- **索引优化**: IVF_FLAT等索引算法
- **实时查询**: 毫秒级向量相似度搜索
- **数据管理**: 完整的CRUD操作支持

### 4. 聚类分析
- **多算法支持**: KMeans、DBSCAN、HDBSCAN
- **自动调优**: 智能确定最优聚类数量
- **质量评估**: 轮廓系数、CH指数等评估指标
- **可视化**: 2D/3D降维可视化

### 5. 话题发现
- **自动识别**: 从聚类中自动提取话题
- **关键词提取**: TF-IDF和词频分析
- **话题关系**: 构建话题关系图谱
- **趋势分析**: 话题演化和新兴话题发现

### 6. 智能分析
- **本地LLM**: 集成Qwen3-4B等本地大模型
- **情感分析**: 自动识别客户情感倾向
- **洞察生成**: AI驱动的业务洞察提取
- **报告生成**: 自动化分析报告

## 快速开始

### 环境要求

- Python 3.8+
- Milvus 2.3+
- 8GB+ RAM (推荐16GB)
- GPU支持 (可选，用于加速)

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd customer-conversation-analysis
```

2. **安装依赖**
```bash
pip install -r requirements.txt
```

3. **配置环境**
```bash
cp .env.example .env
# 编辑.env文件，配置LLM端点和Milvus连接
```

4. **启动Milvus**
```bash
# 使用Docker启动Milvus
docker-compose up -d milvus
```

5. **运行演示**
```bash
python main.py
```

### 配置说明

编辑`.env`文件配置关键参数：

```env
# 本地大模型配置
LLM_ENDPOINT=http://************:9103/v1
LLM_MODEL=Qwen3-4B
LLM_API_KEY=your_api_key_here

# Milvus配置
MILVUS_HOST=localhost
MILVUS_PORT=19530
MILVUS_COLLECTION_NAME=customer_conversations

# 向量化配置
EMBEDDING_MODEL=sentence-transformers/all-MiniLM-L6-v2
EMBEDDING_DIMENSION=384
```

## 使用示例

### 基本用法

```python
from src.langchain_app.pipeline import pipeline

# 1. 初始化流水线
pipeline.initialize()

# 2. 处理对话数据
pipeline.process_conversation_data("data/conversations.json", "json")

# 3. 运行分析
results = pipeline.run_analysis(clustering_method="kmeans", n_clusters=5)

# 4. 查看结果
print(f"发现 {len(results['cluster_analysis'])} 个客户群体")
print(f"主要话题: {[t['title'] for t in results['topic_results']['main_topics']]}")
```

### 高级功能

```python
# 搜索相似对话
similar_conversations = pipeline.search_similar_conversations(
    "产品价格咨询", top_k=10
)

# 获取流水线状态
status = pipeline.get_pipeline_status()

# 自定义聚类参数
results = pipeline.run_analysis(
    clustering_method="hdbscan",
    n_clusters=None  # 自动确定
)
```

## 项目结构

```
customer-conversation-analysis/
├── config/                 # 配置文件
│   └── config.py
├── src/                    # 源代码
│   ├── data_processing/    # 数据处理模块
│   ├── vector_db/          # 向量数据库模块
│   ├── langchain_app/      # LangChain应用
│   ├── analysis/           # 分析模块
│   └── utils/              # 工具模块
├── tests/                  # 测试文件
├── data/                   # 数据目录
├── logs/                   # 日志目录
├── requirements.txt        # 依赖列表
├── .env                    # 环境配置
├── main.py                 # 主程序
└── README.md              # 项目文档
```

## 测试

运行单元测试：

```bash
python -m pytest tests/ -v
```

运行特定测试：

```bash
python tests/test_pipeline.py
```

## 性能优化

### 向量化优化
- 使用GPU加速文本编码
- 批量处理减少I/O开销
- 模型量化降低内存使用

### 聚类优化
- PCA降维减少计算复杂度
- 并行化聚类算法
- 增量聚类支持实时更新

### 存储优化
- Milvus索引参数调优
- 分片策略优化查询性能
- 数据压缩减少存储空间

## 扩展开发

### 添加新的聚类算法

```python
# 在customer_analyzer.py中添加
def perform_clustering(self, embeddings, method="new_algorithm"):
    if method == "new_algorithm":
        # 实现新算法
        pass
```

### 集成新的向量模型

```python
# 在text_embedder.py中扩展
class TextEmbedder:
    def __init__(self, model_name="new-model"):
        # 支持新模型
        pass
```

### 自定义话题发现

```python
# 在topic_discovery.py中扩展
class TopicDiscovery:
    def custom_topic_extraction(self, texts):
        # 自定义话题提取逻辑
        pass
```

## 常见问题

### Q: Milvus连接失败怎么办？
A: 检查Milvus服务状态，确认端口配置正确，查看防火墙设置。

### Q: 内存不足怎么处理？
A: 减少batch_size，使用PCA降维，或者分批处理数据。

### Q: 聚类效果不好怎么优化？
A: 调整聚类参数，尝试不同算法，增加数据预处理步骤。

### Q: 如何添加自定义数据源？
A: 在preprocessor.py中扩展load_conversation_data方法。

## 贡献指南

1. Fork项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启Pull Request

## 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

- 项目维护者: [Your Name]
- 邮箱: [<EMAIL>]
- 项目链接: [https://github.com/yourusername/customer-conversation-analysis]

## 致谢

- [Milvus](https://milvus.io/) - 高性能向量数据库
- [LangChain](https://langchain.com/) - LLM应用开发框架
- [SentenceTransformers](https://www.sbert.net/) - 文本嵌入模型
- [scikit-learn](https://scikit-learn.org/) - 机器学习工具包
