"""
Milvus向量数据库客户端
"""
import logging
from typing import List, Dict, Any, Optional
from pymilvus import (
    connections,
    utility,
    FieldSchema,
    CollectionSchema,
    DataType,
    Collection,
)
from config.config import config


class MilvusClient:
    """Milvus向量数据库客户端"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.collection = None
        self._connected = False
    
    def connect(self) -> bool:
        """
        连接到Milvus服务器
        
        Returns:
            bool: 连接是否成功
        """
        try:
            connections.connect(
                alias="default",
                host=config.milvus.host,
                port=config.milvus.port,
                user=config.milvus.user,
                password=config.milvus.password
            )
            self._connected = True
            self.logger.info(f"成功连接到Milvus服务器: {config.milvus.host}:{config.milvus.port}")
            return True
        except Exception as e:
            self.logger.error(f"连接Milvus失败: {e}")
            return False
    
    def create_collection_schema(self) -> CollectionSchema:
        """
        创建collection的schema
        
        Returns:
            CollectionSchema: 集合schema
        """
        fields = [
            FieldSchema(
                name="id",
                dtype=DataType.INT64,
                is_primary=True,
                auto_id=True,
                description="主键ID"
            ),
            FieldSchema(
                name="conversation_id",
                dtype=DataType.VARCHAR,
                max_length=100,
                description="对话ID"
            ),
            FieldSchema(
                name="customer_id",
                dtype=DataType.VARCHAR,
                max_length=100,
                description="客户ID"
            ),
            FieldSchema(
                name="text",
                dtype=DataType.VARCHAR,
                max_length=2000,
                description="对话文本"
            ),
            FieldSchema(
                name="timestamp",
                dtype=DataType.INT64,
                description="时间戳"
            ),
            FieldSchema(
                name="embedding",
                dtype=DataType.FLOAT_VECTOR,
                dim=config.embedding.dimension,
                description="文本向量"
            ),
        ]
        
        schema = CollectionSchema(
            fields=fields,
            description="客户对话分析集合"
        )
        
        return schema
    
    def create_collection(self) -> bool:
        """
        创建collection
        
        Returns:
            bool: 创建是否成功
        """
        try:
            if not self._connected:
                if not self.connect():
                    return False
            
            # 检查collection是否已存在
            if utility.has_collection(config.milvus.collection_name):
                self.logger.info(f"Collection {config.milvus.collection_name} 已存在")
                self.collection = Collection(config.milvus.collection_name)
                return True
            
            # 创建新的collection
            schema = self.create_collection_schema()
            self.collection = Collection(
                name=config.milvus.collection_name,
                schema=schema,
                consistency_level="Strong"
            )
            
            self.logger.info(f"成功创建Collection: {config.milvus.collection_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"创建Collection失败: {e}")
            return False
    
    def create_index(self) -> bool:
        """
        创建向量索引
        
        Returns:
            bool: 创建是否成功
        """
        try:
            if not self.collection:
                self.logger.error("Collection未初始化")
                return False
            
            index_params = {
                "index_type": config.embedding.index_type,
                "metric_type": config.embedding.metric_type,
                "params": {"nlist": 128}
            }
            
            self.collection.create_index(
                field_name="embedding",
                index_params=index_params
            )
            
            self.logger.info("成功创建向量索引")
            return True
            
        except Exception as e:
            self.logger.error(f"创建索引失败: {e}")
            return False
    
    def load_collection(self) -> bool:
        """
        加载collection到内存
        
        Returns:
            bool: 加载是否成功
        """
        try:
            if not self.collection:
                self.logger.error("Collection未初始化")
                return False
            
            self.collection.load()
            self.logger.info("成功加载Collection到内存")
            return True
            
        except Exception as e:
            self.logger.error(f"加载Collection失败: {e}")
            return False
    
    def insert_data(self, data: List[List[Any]]) -> bool:
        """
        插入数据到collection
        
        Args:
            data: 要插入的数据，格式为 [conversation_ids, customer_ids, texts, timestamps, embeddings]
        
        Returns:
            bool: 插入是否成功
        """
        try:
            if not self.collection:
                self.logger.error("Collection未初始化")
                return False
            
            insert_result = self.collection.insert(data)
            self.logger.info(f"成功插入 {len(data[0])} 条数据")
            return True
            
        except Exception as e:
            self.logger.error(f"插入数据失败: {e}")
            return False
    
    def search(self, query_vectors: List[List[float]], limit: int = 10, 
               expr: Optional[str] = None) -> List[Any]:
        """
        向量搜索
        
        Args:
            query_vectors: 查询向量
            limit: 返回结果数量
            expr: 过滤表达式
        
        Returns:
            搜索结果
        """
        try:
            if not self.collection:
                self.logger.error("Collection未初始化")
                return []
            
            search_params = {
                "metric_type": config.embedding.metric_type,
                "params": {"nprobe": 10}
            }
            
            results = self.collection.search(
                data=query_vectors,
                anns_field="embedding",
                param=search_params,
                limit=limit,
                expr=expr,
                output_fields=["conversation_id", "customer_id", "text", "timestamp"]
            )
            
            return results
            
        except Exception as e:
            self.logger.error(f"搜索失败: {e}")
            return []
    
    def get_collection_stats(self) -> Dict[str, Any]:
        """
        获取collection统计信息
        
        Returns:
            统计信息字典
        """
        try:
            if not self.collection:
                return {}
            
            stats = {
                "name": self.collection.name,
                "num_entities": self.collection.num_entities,
                "description": self.collection.description
            }
            
            return stats
            
        except Exception as e:
            self.logger.error(f"获取统计信息失败: {e}")
            return {}
    
    def disconnect(self):
        """断开连接"""
        try:
            connections.disconnect("default")
            self._connected = False
            self.logger.info("已断开Milvus连接")
        except Exception as e:
            self.logger.error(f"断开连接失败: {e}")
