"""
数据预处理模块
"""
import logging
import re
import json
import pandas as pd
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
import jieba
import nltk
from config.config import config


class ConversationPreprocessor:
    """客户对话数据预处理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self._setup_nltk()
    
    def _setup_nltk(self):
        """设置NLTK"""
        try:
            # 下载必要的NLTK数据
            nltk.download('punkt', quiet=True)
            nltk.download('stopwords', quiet=True)
        except Exception as e:
            self.logger.warning(f"NLTK设置失败: {e}")
    
    def load_conversation_data(self, file_path: str, file_format: str = "json") -> List[Dict[str, Any]]:
        """
        加载对话数据
        
        Args:
            file_path: 文件路径
            file_format: 文件格式 (json, csv, excel)
        
        Returns:
            List[Dict]: 对话数据列表
        """
        try:
            if file_format.lower() == "json":
                return self._load_json_data(file_path)
            elif file_format.lower() == "csv":
                return self._load_csv_data(file_path)
            elif file_format.lower() in ["excel", "xlsx"]:
                return self._load_excel_data(file_path)
            else:
                self.logger.error(f"不支持的文件格式: {file_format}")
                return []
        except Exception as e:
            self.logger.error(f"加载数据失败: {e}")
            return []
    
    def _load_json_data(self, file_path: str) -> List[Dict[str, Any]]:
        """加载JSON数据"""
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        if isinstance(data, list):
            return data
        elif isinstance(data, dict):
            return [data]
        else:
            self.logger.error("JSON数据格式不正确")
            return []
    
    def _load_csv_data(self, file_path: str) -> List[Dict[str, Any]]:
        """加载CSV数据"""
        df = pd.read_csv(file_path)
        return df.to_dict('records')
    
    def _load_excel_data(self, file_path: str) -> List[Dict[str, Any]]:
        """加载Excel数据"""
        df = pd.read_excel(file_path)
        return df.to_dict('records')
    
    def preprocess_conversations(self, conversations: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        预处理对话数据
        
        Args:
            conversations: 原始对话数据
        
        Returns:
            List[Dict]: 预处理后的对话数据
        """
        processed_conversations = []
        
        for conversation in conversations:
            try:
                processed_conv = self._preprocess_single_conversation(conversation)
                if processed_conv:
                    processed_conversations.append(processed_conv)
            except Exception as e:
                self.logger.warning(f"预处理单个对话失败: {e}")
                continue
        
        self.logger.info(f"成功预处理 {len(processed_conversations)} 个对话")
        return processed_conversations
    
    def _preprocess_single_conversation(self, conversation: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        预处理单个对话
        
        Args:
            conversation: 单个对话数据
        
        Returns:
            Optional[Dict]: 预处理后的对话数据
        """
        # 提取必要字段
        conversation_id = conversation.get('conversation_id', '')
        customer_id = conversation.get('customer_id', '')
        text = conversation.get('text', '')
        timestamp = conversation.get('timestamp', '')
        
        # 验证必要字段
        if not all([conversation_id, customer_id, text]):
            self.logger.warning("对话缺少必要字段")
            return None
        
        # 清理文本
        cleaned_text = self._clean_text(text)
        if not cleaned_text:
            return None
        
        # 处理时间戳
        processed_timestamp = self._process_timestamp(timestamp)
        
        return {
            'conversation_id': str(conversation_id),
            'customer_id': str(customer_id),
            'text': cleaned_text,
            'timestamp': processed_timestamp,
            'original_text': text,
            'text_length': len(cleaned_text),
            'word_count': len(cleaned_text.split())
        }
    
    def _clean_text(self, text: str) -> str:
        """
        清理文本
        
        Args:
            text: 原始文本
        
        Returns:
            str: 清理后的文本
        """
        if not text or not isinstance(text, str):
            return ""
        
        # 移除多余空白字符
        text = re.sub(r'\s+', ' ', text.strip())
        
        # 移除特殊字符（保留中文、英文、数字、基本标点）
        text = re.sub(r'[^\u4e00-\u9fff\w\s.,!?;:()""''""（）。，！？；：]', '', text)
        
        # 长度检查
        if len(text) < config.data_processing.min_text_length:
            return ""
        
        # 截断过长文本
        if len(text) > config.data_processing.max_text_length:
            text = text[:config.data_processing.max_text_length]
        
        return text.strip()
    
    def _process_timestamp(self, timestamp: Any) -> int:
        """
        处理时间戳
        
        Args:
            timestamp: 时间戳（可能是字符串、数字或datetime对象）
        
        Returns:
            int: Unix时间戳
        """
        try:
            if isinstance(timestamp, (int, float)):
                return int(timestamp)
            elif isinstance(timestamp, str):
                # 尝试解析常见的时间格式
                for fmt in ['%Y-%m-%d %H:%M:%S', '%Y-%m-%d', '%Y/%m/%d %H:%M:%S', '%Y/%m/%d']:
                    try:
                        dt = datetime.strptime(timestamp, fmt)
                        return int(dt.timestamp())
                    except ValueError:
                        continue
                # 如果是纯数字字符串
                if timestamp.isdigit():
                    return int(timestamp)
            elif hasattr(timestamp, 'timestamp'):
                return int(timestamp.timestamp())
        except Exception as e:
            self.logger.warning(f"时间戳处理失败: {e}")
        
        # 返回当前时间戳作为默认值
        return int(datetime.now().timestamp())
    
    def extract_keywords(self, text: str, language: str = "auto") -> List[str]:
        """
        提取关键词
        
        Args:
            text: 输入文本
            language: 语言类型 (auto, zh, en)
        
        Returns:
            List[str]: 关键词列表
        """
        try:
            if not text:
                return []
            
            # 自动检测语言
            if language == "auto":
                language = self._detect_language(text)
            
            if language == "zh":
                # 中文分词
                words = jieba.lcut(text)
                # 过滤停用词和短词
                keywords = [word for word in words if len(word) > 1 and word.strip()]
            else:
                # 英文分词
                words = nltk.word_tokenize(text.lower())
                # 过滤停用词
                from nltk.corpus import stopwords
                stop_words = set(stopwords.words('english'))
                keywords = [word for word in words if word.isalpha() and word not in stop_words]
            
            return keywords[:20]  # 返回前20个关键词
            
        except Exception as e:
            self.logger.warning(f"关键词提取失败: {e}")
            return []
    
    def _detect_language(self, text: str) -> str:
        """
        简单的语言检测
        
        Args:
            text: 输入文本
        
        Returns:
            str: 语言代码 (zh, en)
        """
        # 检测中文字符
        chinese_chars = re.findall(r'[\u4e00-\u9fff]', text)
        if len(chinese_chars) > len(text) * 0.3:
            return "zh"
        else:
            return "en"
    
    def create_conversation_batches(self, conversations: List[Dict[str, Any]], 
                                  batch_size: Optional[int] = None) -> List[List[Dict[str, Any]]]:
        """
        创建对话批次
        
        Args:
            conversations: 对话列表
            batch_size: 批次大小
        
        Returns:
            List[List[Dict]]: 批次列表
        """
        batch_size = batch_size or config.data_processing.batch_size
        batches = []
        
        for i in range(0, len(conversations), batch_size):
            batch = conversations[i:i + batch_size]
            batches.append(batch)
        
        self.logger.info(f"创建了 {len(batches)} 个批次，每批次最多 {batch_size} 个对话")
        return batches
    
    def validate_conversation_data(self, conversations: List[Dict[str, Any]]) -> Tuple[List[Dict[str, Any]], List[str]]:
        """
        验证对话数据
        
        Args:
            conversations: 对话数据列表
        
        Returns:
            Tuple[List[Dict], List[str]]: (有效数据, 错误信息)
        """
        valid_conversations = []
        errors = []
        
        required_fields = ['conversation_id', 'customer_id', 'text', 'timestamp']
        
        for i, conversation in enumerate(conversations):
            # 检查必要字段
            missing_fields = [field for field in required_fields if field not in conversation]
            if missing_fields:
                errors.append(f"对话 {i}: 缺少字段 {missing_fields}")
                continue
            
            # 检查文本长度
            text = conversation.get('text', '')
            if len(text) < config.data_processing.min_text_length:
                errors.append(f"对话 {i}: 文本过短")
                continue
            
            valid_conversations.append(conversation)
        
        self.logger.info(f"验证完成: {len(valid_conversations)} 个有效对话, {len(errors)} 个错误")
        return valid_conversations, errors
    
    def get_statistics(self, conversations: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        获取数据统计信息
        
        Args:
            conversations: 对话数据列表
        
        Returns:
            Dict: 统计信息
        """
        if not conversations:
            return {}
        
        texts = [conv.get('text', '') for conv in conversations]
        text_lengths = [len(text) for text in texts]
        word_counts = [len(text.split()) for text in texts]
        
        stats = {
            'total_conversations': len(conversations),
            'unique_customers': len(set(conv.get('customer_id', '') for conv in conversations)),
            'avg_text_length': sum(text_lengths) / len(text_lengths),
            'max_text_length': max(text_lengths),
            'min_text_length': min(text_lengths),
            'avg_word_count': sum(word_counts) / len(word_counts),
            'max_word_count': max(word_counts),
            'min_word_count': min(word_counts)
        }
        
        return stats
