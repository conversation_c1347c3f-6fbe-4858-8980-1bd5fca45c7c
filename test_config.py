"""
测试配置和依赖是否正常
"""
import sys
import traceback

def test_imports():
    """测试关键模块导入"""
    print("测试模块导入...")
    
    try:
        # 测试配置模块
        print("1. 测试配置模块...")
        from config.config import config
        print(f"   ✅ 配置加载成功")
        print(f"   - LLM模型: {config.llm.model}")
        print(f"   - LLM端点: {config.llm.endpoint}")
        print(f"   - API Key: {config.llm.api_key}")
        
        # 测试Pydantic
        print("2. 测试Pydantic...")
        try:
            from pydantic import Field
            from pydantic_settings import BaseSettings
            print("   ✅ Pydantic v2 导入成功")
        except ImportError:
            from pydantic import BaseSettings, Field
            print("   ✅ Pydantic v1 导入成功")
        
        # 测试其他关键依赖
        print("3. 测试其他依赖...")
        
        try:
            import pymilvus
            print("   ✅ PyMilvus 导入成功")
        except ImportError as e:
            print(f"   ❌ PyMilvus 导入失败: {e}")
        
        try:
            import langchain
            print("   ✅ LangChain 导入成功")
        except ImportError as e:
            print(f"   ❌ LangChain 导入失败: {e}")
        
        try:
            import sentence_transformers
            print("   ✅ SentenceTransformers 导入成功")
        except ImportError as e:
            print(f"   ❌ SentenceTransformers 导入失败: {e}")
        
        try:
            import sklearn
            print("   ✅ Scikit-learn 导入成功")
        except ImportError as e:
            print(f"   ❌ Scikit-learn 导入失败: {e}")
        
        try:
            import numpy
            print("   ✅ NumPy 导入成功")
        except ImportError as e:
            print(f"   ❌ NumPy 导入失败: {e}")
        
        try:
            import pandas
            print("   ✅ Pandas 导入成功")
        except ImportError as e:
            print(f"   ❌ Pandas 导入失败: {e}")
        
        print("\n4. 测试项目模块...")
        
        try:
            from src.utils.logger import setup_logger
            print("   ✅ 日志模块导入成功")
        except ImportError as e:
            print(f"   ❌ 日志模块导入失败: {e}")
        
        try:
            from src.data_processing.preprocessor import ConversationPreprocessor
            print("   ✅ 数据预处理模块导入成功")
        except ImportError as e:
            print(f"   ❌ 数据预处理模块导入失败: {e}")
        
        try:
            from src.vector_db.milvus_client import MilvusClient
            print("   ✅ Milvus客户端模块导入成功")
        except ImportError as e:
            print(f"   ❌ Milvus客户端模块导入失败: {e}")
        
        try:
            from src.langchain_app.llm_client import LocalLLMClient
            print("   ✅ LLM客户端模块导入成功")
        except ImportError as e:
            print(f"   ❌ LLM客户端模块导入失败: {e}")
        
        print("\n✅ 所有关键模块导入测试完成!")
        return True
        
    except Exception as e:
        print(f"\n❌ 导入测试失败: {e}")
        print(f"错误详情:\n{traceback.format_exc()}")
        return False

def test_llm_client():
    """测试LLM客户端初始化"""
    print("\n测试LLM客户端初始化...")
    
    try:
        from src.langchain_app.llm_client import LocalLLMClient
        
        # 创建客户端实例
        client = LocalLLMClient()
        
        # 检查初始化状态
        if client.llm is not None:
            print("   ✅ LLM客户端初始化成功")
            
            # 获取模型信息
            info = client.get_model_info()
            print(f"   - 模型名称: {info.get('model_name')}")
            print(f"   - 端点: {info.get('endpoint')}")
            print(f"   - 初始化状态: {info.get('is_initialized')}")
            
            return True
        else:
            print("   ❌ LLM客户端初始化失败")
            return False
            
    except Exception as e:
        print(f"   ❌ LLM客户端测试失败: {e}")
        print(f"错误详情:\n{traceback.format_exc()}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("客户对话分析流水线 - 配置和依赖测试")
    print("=" * 60)
    
    # 测试导入
    import_success = test_imports()
    
    if import_success:
        # 测试LLM客户端
        llm_success = test_llm_client()
        
        if llm_success:
            print("\n🎉 所有测试通过! 系统配置正常。")
            print("\n现在可以运行 'python main.py' 来启动完整的分析流水线。")
        else:
            print("\n⚠️  LLM客户端测试失败，请检查网络连接和端点配置。")
    else:
        print("\n❌ 导入测试失败，请检查依赖安装。")
        print("\n建议运行: pip install -r requirements.txt")
    
    print("\n" + "=" * 60)

if __name__ == "__main__":
    main()
